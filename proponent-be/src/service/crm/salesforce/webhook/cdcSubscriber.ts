import { getSalesforceClient } from '../salesforceOauthServices';
import { supabaseAdminClient } from '@/db/supabaseClient';
import * as jsforce from 'jsforce';
import { Subscription } from 'faye';
import { handleContactCreation, handleCompanyCreation, handleContactPropertyChange, handleContactAssociationChange } from './salesforceWebhookServices';
import { SalesforceCDCEvent, SalesforceRawCDCEvent, SalesforceContactEvent, SalesforceAccountEvent, SalesforceAssociationEvent, SalesforceOpportunityContactRoleSubject } from './types';

export class SalesforceCDCSubscriber {
  // Reconnection configuration constants
  private static readonly MAX_RECONNECT_ATTEMPTS = 5;
  private static readonly INITIAL_RECONNECT_DELAY_MS = 5000;

  private activeSubscriptions: Map<string, Subscription> = new Map();
  private connectionHealth: Map<number, boolean> = new Map();
  private reconnectAttempts: Map<number, number> = new Map();

  async startCDCSubscription(tenantId: number) {
    try {
      console.log(`Starting CDC subscription for tenant ${tenantId}`);

      const salesforceClient = await getSalesforceClient({
        supabase: supabaseAdminClient,
        tenantId,
      });

      await this.subscribeToChangeEvents(salesforceClient, tenantId);

      this.connectionHealth.set(tenantId, true);
      this.reconnectAttempts.set(tenantId, 0);

      console.log(`CDC subscription started successfully for tenant ${tenantId}`);
    } catch (error) {
      console.error(`Failed to start CDC subscription for tenant ${tenantId}:`, error);
      this.connectionHealth.set(tenantId, false);

      setTimeout(() => this.attemptReconnection(tenantId), SalesforceCDCSubscriber.INITIAL_RECONNECT_DELAY_MS);
    }
  }

  private async subscribeToChangeEvents(client: jsforce.Connection, tenantId: number) {
   const channels = [
    '/data/ContactChangeEvent',
    '/data/AccountChangeEvent',
    '/data/OpportunityChangeEvent',
    '/data/AccountContactRelationChangeEvent',
    '/data/AccountContactRoleChangeEvent',
    '/data/OpportunityContactRoleChangeEvent',
];


    for (const channel of channels) {
      try {
        const subscription = client.streaming.subscribe(channel, (message: SalesforceRawCDCEvent) => {
          console.log(`[Tenant ${tenantId}] Received CDC event on ${channel}:`, JSON.stringify(message, null, 2));
          this.processCDCEvent(message, tenantId);
        });

        this.activeSubscriptions.set(`${tenantId}-${channel}`, subscription);
        console.log(`[Tenant ${tenantId}] Successfully subscribed to ${channel}`);
      } catch (error) {
        console.error(`[Tenant ${tenantId}] Failed to subscribe to ${channel}:`, error);
        this.handleSubscriptionError(tenantId, channel, error);
      }
    }
  }

  async processCDCEvent(message: SalesforceRawCDCEvent, tenantId: number) {
    try {
      const transformedEvent = await this.transformCDCEvent(message, tenantId);
      this.processWebhookEvent(transformedEvent);
    } catch (error) {
      console.error(`[Tenant ${tenantId}] Error processing CDC event:`, error);
    }
  }

  private async transformCDCEvent(cdcEvent: SalesforceRawCDCEvent, tenantId: number): Promise<SalesforceCDCEvent> {
    const payload = cdcEvent.payload;
    const changeHeader = payload.ChangeEventHeader;

    const recordId = changeHeader.recordIds && changeHeader.recordIds.length > 0 ? changeHeader.recordIds[0] : payload.Id;

    // Validate that we have a valid recordId
    if (!recordId || recordId.trim() === '') {
      const errorMsg = `[Tenant ${tenantId}] Invalid or missing recordId in CDC event for ${changeHeader.entityName}. RecordIds: ${JSON.stringify(changeHeader.recordIds)}, Payload.Id: ${payload.Id}`;
      console.error(errorMsg);
      throw new Error(errorMsg);
    }

    // Extract all fields except ChangeEventHeader
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
    const { ChangeEventHeader, ...fieldValues } = payload;

    // Flatten Salesforce Name object structure for Contact entities
    const processedFieldValues =
      changeHeader.entityName === 'Contact' && fieldValues.Name && typeof fieldValues.Name === 'object'
        ? {
            ...fieldValues,
            FirstName: fieldValues.Name.FirstName,
            LastName: fieldValues.Name.LastName,
            Salutation: fieldValues.Name.Salutation,
          }
        : fieldValues;

    return {
      eventType: this.mapChangeType(changeHeader.changeType),
      subject: {
        Id: recordId,
        attributes: {
          type: changeHeader.entityName,
        },
        ...processedFieldValues,
      },
      tenantId,
      changeEventHeader: {
        entityName: changeHeader.entityName,
        changeType: changeHeader.changeType,
        changedFields: changeHeader.changedFields || [],
        recordIds: changeHeader.recordIds || [],
        transactionKey: changeHeader.transactionKey,
        commitTimestamp: changeHeader.commitTimestamp,
        commitUser: changeHeader.commitUser,
        commitNumber: changeHeader.commitNumber,
        sequenceNumber: changeHeader.sequenceNumber,
        nulledFields: changeHeader.nulledFields || [],
        changeOrigin: changeHeader.changeOrigin,
      },
    };
  }

  private mapChangeType(changeType: string): 'created' | 'updated' | 'deleted' {
    switch (changeType) {
      case 'CREATE':
        return 'created';
      case 'UPDATE':
        return 'updated';
      case 'DELETE':
        return 'deleted';
      default:
        return changeType.toLowerCase() as 'created' | 'updated' | 'deleted';
    }
  }

  private async processWebhookEvent(event: SalesforceCDCEvent) {
    try {
      console.log(`[Tenant ${event.tenantId}] Processing CDC event: ${event.eventType} for ${event.subject?.attributes?.type}`);

      const entityType = event.subject?.attributes?.type;

      switch (event.eventType) {
        case 'created':
          if (entityType === 'Contact') await handleContactCreation(event as SalesforceContactEvent);
          else if (entityType === 'Account') await handleCompanyCreation(event as SalesforceAccountEvent);
          else if (entityType === 'OpportunityContactRole') await this.handleOpportunityContactRoleChange(event, false);

          break;
        case 'updated':
          if (entityType === 'Contact') await handleContactPropertyChange(event as SalesforceContactEvent);
          else if (entityType === 'OpportunityContactRole') await this.handleOpportunityContactRoleChange(event, false);

          break;
        case 'deleted':
          if (entityType === 'OpportunityContactRole') await this.handleOpportunityContactRoleChange(event, true);
          else console.log(`[Tenant ${event.tenantId}] Handling deletion for ${entityType}`);

          break;
        default:
          console.log(`[Tenant ${event.tenantId}] Unhandled CDC event: ${event.eventType} for ${entityType}`);
      }
    } catch (error) {
      console.error(`[Tenant ${event.tenantId}] Error processing CDC event ${event.eventType}:`, error);
    }
  }

  private async handleOpportunityContactRoleChange(event: SalesforceCDCEvent, isRemoved: boolean) {
    try {
      const opportunityContactRoleSubject = event.subject as SalesforceOpportunityContactRoleSubject;

      const associationEvent: SalesforceAssociationEvent = {
        ...event,
        fromObjectId: opportunityContactRoleSubject.ContactId,
        toObjectId: opportunityContactRoleSubject.OpportunityId,
        relationshipName: 'Contact_Opportunity',
        eventType: isRemoved ? 'deleted' : event.eventType,
      } as SalesforceAssociationEvent;

      await handleContactAssociationChange(associationEvent);
    } catch (error) {
      console.error(`[Tenant ${event.tenantId}] Error handling OpportunityContactRole change:`, error);
    }
  }

  private handleSubscriptionError(tenantId: number, channel: string, error: any) {
    console.error(`[Tenant ${tenantId}] Subscription error on ${channel}:`, error);
    this.connectionHealth.set(tenantId, false);

    this.activeSubscriptions.delete(`${tenantId}-${channel}`);

    setTimeout(() => this.attemptReconnection(tenantId), SalesforceCDCSubscriber.INITIAL_RECONNECT_DELAY_MS);
  }

  private async attemptReconnection(tenantId: number) {
    const attempts = this.reconnectAttempts.get(tenantId) || 0;

    if (attempts >= SalesforceCDCSubscriber.MAX_RECONNECT_ATTEMPTS) {
      console.error(`[Tenant ${tenantId}] Max reconnection attempts reached. Giving up.`);
      return;
    }

    console.log(`[Tenant ${tenantId}] Attempting reconnection (attempt ${attempts + 1}/${SalesforceCDCSubscriber.MAX_RECONNECT_ATTEMPTS})`);
    this.reconnectAttempts.set(tenantId, attempts + 1);

    try {
      await this.stopCDCSubscription(tenantId);
      await this.startCDCSubscription(tenantId);
    } catch (error) {
      console.error(`[Tenant ${tenantId}] Reconnection attempt failed:`, error);

      const delay = SalesforceCDCSubscriber.INITIAL_RECONNECT_DELAY_MS * Math.pow(2, attempts);
      setTimeout(() => this.attemptReconnection(tenantId), delay);
    }
  }

  async stopCDCSubscription(tenantId: number) {
    console.log(`[Tenant ${tenantId}] Stopping CDC subscriptions`);

    for (const [key, subscription] of this.activeSubscriptions.entries()) {
      if (key.startsWith(`${tenantId}-`)) {
        try {
          subscription.cancel();
          console.log(`[Tenant ${tenantId}] Cancelled subscription: ${key}`);
        } catch (error) {
          console.error(`[Tenant ${tenantId}] Error cancelling subscription ${key}:`, error);
        }
        this.activeSubscriptions.delete(key);
      }
    }

    this.connectionHealth.set(tenantId, false);
    this.reconnectAttempts.delete(tenantId);
  }

  isHealthy(tenantId: number): boolean {
    return this.connectionHealth.get(tenantId) || false;
  }

  getActiveSubscriptions(): string[] {
    return Array.from(this.activeSubscriptions.keys());
  }

  async stopAllSubscriptions() {
    console.log('Stopping all CDC subscriptions');

    for (const [key, subscription] of this.activeSubscriptions.entries()) {
      try {
        subscription.cancel();
        console.log(`Cancelled subscription: ${key}`);
      } catch (error) {
        console.error(`Error cancelling subscription ${key}:`, error);
      }
    }

    this.activeSubscriptions.clear();
    this.connectionHealth.clear();
    this.reconnectAttempts.clear();
  }
}

export const cdcSubscriber = new SalesforceCDCSubscriber();
